// Generated by view binder compiler. Do not edit!
package com.muslimcore.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.muslimcore.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentPrayerBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final MaterialButton buttonLocation;

  @NonNull
  public final ImageButton buttonNextDay;

  @NonNull
  public final Button buttonNotificationSettings;

  @NonNull
  public final ImageButton buttonPreviousDay;

  @NonNull
  public final MaterialButton buttonTrackPrayers;

  @NonNull
  public final LinearLayout headerLayout;

  @NonNull
  public final RecyclerView recyclerViewPrayerTimes;

  @NonNull
  public final TextView textCurrentPrayer;

  @NonNull
  public final TextView textCurrentPrayerPeriod;

  @NonNull
  public final TextView textCurrentPrayerTime;

  @NonNull
  public final TextView textError;

  @NonNull
  public final TextView textHijriDate;

  @NonNull
  public final TextView textSelectedDate;

  private FragmentPrayerBinding(@NonNull NestedScrollView rootView,
      @NonNull MaterialButton buttonLocation, @NonNull ImageButton buttonNextDay,
      @NonNull Button buttonNotificationSettings, @NonNull ImageButton buttonPreviousDay,
      @NonNull MaterialButton buttonTrackPrayers, @NonNull LinearLayout headerLayout,
      @NonNull RecyclerView recyclerViewPrayerTimes, @NonNull TextView textCurrentPrayer,
      @NonNull TextView textCurrentPrayerPeriod, @NonNull TextView textCurrentPrayerTime,
      @NonNull TextView textError, @NonNull TextView textHijriDate,
      @NonNull TextView textSelectedDate) {
    this.rootView = rootView;
    this.buttonLocation = buttonLocation;
    this.buttonNextDay = buttonNextDay;
    this.buttonNotificationSettings = buttonNotificationSettings;
    this.buttonPreviousDay = buttonPreviousDay;
    this.buttonTrackPrayers = buttonTrackPrayers;
    this.headerLayout = headerLayout;
    this.recyclerViewPrayerTimes = recyclerViewPrayerTimes;
    this.textCurrentPrayer = textCurrentPrayer;
    this.textCurrentPrayerPeriod = textCurrentPrayerPeriod;
    this.textCurrentPrayerTime = textCurrentPrayerTime;
    this.textError = textError;
    this.textHijriDate = textHijriDate;
    this.textSelectedDate = textSelectedDate;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentPrayerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentPrayerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_prayer, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentPrayerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_location;
      MaterialButton buttonLocation = ViewBindings.findChildViewById(rootView, id);
      if (buttonLocation == null) {
        break missingId;
      }

      id = R.id.button_next_day;
      ImageButton buttonNextDay = ViewBindings.findChildViewById(rootView, id);
      if (buttonNextDay == null) {
        break missingId;
      }

      id = R.id.button_notification_settings;
      Button buttonNotificationSettings = ViewBindings.findChildViewById(rootView, id);
      if (buttonNotificationSettings == null) {
        break missingId;
      }

      id = R.id.button_previous_day;
      ImageButton buttonPreviousDay = ViewBindings.findChildViewById(rootView, id);
      if (buttonPreviousDay == null) {
        break missingId;
      }

      id = R.id.button_track_prayers;
      MaterialButton buttonTrackPrayers = ViewBindings.findChildViewById(rootView, id);
      if (buttonTrackPrayers == null) {
        break missingId;
      }

      id = R.id.header_layout;
      LinearLayout headerLayout = ViewBindings.findChildViewById(rootView, id);
      if (headerLayout == null) {
        break missingId;
      }

      id = R.id.recycler_view_prayer_times;
      RecyclerView recyclerViewPrayerTimes = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewPrayerTimes == null) {
        break missingId;
      }

      id = R.id.text_current_prayer;
      TextView textCurrentPrayer = ViewBindings.findChildViewById(rootView, id);
      if (textCurrentPrayer == null) {
        break missingId;
      }

      id = R.id.text_current_prayer_period;
      TextView textCurrentPrayerPeriod = ViewBindings.findChildViewById(rootView, id);
      if (textCurrentPrayerPeriod == null) {
        break missingId;
      }

      id = R.id.text_current_prayer_time;
      TextView textCurrentPrayerTime = ViewBindings.findChildViewById(rootView, id);
      if (textCurrentPrayerTime == null) {
        break missingId;
      }

      id = R.id.text_error;
      TextView textError = ViewBindings.findChildViewById(rootView, id);
      if (textError == null) {
        break missingId;
      }

      id = R.id.text_hijri_date;
      TextView textHijriDate = ViewBindings.findChildViewById(rootView, id);
      if (textHijriDate == null) {
        break missingId;
      }

      id = R.id.text_selected_date;
      TextView textSelectedDate = ViewBindings.findChildViewById(rootView, id);
      if (textSelectedDate == null) {
        break missingId;
      }

      return new FragmentPrayerBinding((NestedScrollView) rootView, buttonLocation, buttonNextDay,
          buttonNotificationSettings, buttonPreviousDay, buttonTrackPrayers, headerLayout,
          recyclerViewPrayerTimes, textCurrentPrayer, textCurrentPrayerPeriod,
          textCurrentPrayerTime, textError, textHijriDate, textSelectedDate);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
