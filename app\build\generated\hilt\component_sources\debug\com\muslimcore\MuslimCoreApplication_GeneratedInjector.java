package com.muslimcore;

import dagger.hilt.InstallIn;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.components.SingletonComponent;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = MuslimCoreApplication.class
)
@GeneratedEntryPoint
@InstallIn(SingletonComponent.class)
public interface MuslimCoreApplication_GeneratedInjector {
  void injectMuslimCoreApplication(MuslimCoreApplication muslimCoreApplication);
}
