<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_notification_settings" modulePackage="com.muslimcore" filePath="app\src\main\res\layout\fragment_notification_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_notification_settings_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="262" endOffset="12"/></Target><Target id="@+id/button_back" view="ImageView"><Expressions/><location startLine="22" startOffset="12" endLine="29" endOffset="49"/></Target><Target id="@+id/switch_persistent_notification" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="79" startOffset="16" endLine="87" endOffset="47"/></Target><Target id="@+id/switch_prayer_alerts" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="125" startOffset="16" endLine="134" endOffset="47"/></Target><Target id="@+id/layout_alert_options" view="LinearLayout"><Expressions/><location startLine="137" startOffset="16" endLine="196" endOffset="30"/></Target><Target id="@+id/radio_group_alert_type" view="RadioGroup"><Expressions/><location startLine="153" startOffset="20" endLine="194" endOffset="32"/></Target><Target id="@+id/radio_vibration_only" view="RadioButton"><Expressions/><location startLine="158" startOffset="24" endLine="165" endOffset="72"/></Target><Target id="@+id/radio_athan_sound" view="RadioButton"><Expressions/><location startLine="167" startOffset="24" endLine="174" endOffset="72"/></Target><Target id="@+id/radio_phone_sound" view="RadioButton"><Expressions/><location startLine="176" startOffset="24" endLine="183" endOffset="72"/></Target><Target id="@+id/radio_vibration_and_sound" view="RadioButton"><Expressions/><location startLine="185" startOffset="24" endLine="192" endOffset="72"/></Target><Target id="@+id/switch_fullscreen_alert" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="234" startOffset="16" endLine="242" endOffset="47"/></Target><Target id="@+id/button_save_settings" view="Button"><Expressions/><location startLine="249" startOffset="8" endLine="258" endOffset="38"/></Target></Targets></Layout>