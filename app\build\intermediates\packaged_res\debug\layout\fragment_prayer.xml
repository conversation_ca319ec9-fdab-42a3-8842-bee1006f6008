<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#1A1A1A"
    android:fillViewport="true"
    app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="0dp">

            <!-- Header Section with Current Prayer -->
            <LinearLayout
                android:id="@+id/header_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/mosque_background"
                android:orientation="vertical"
                android:padding="20dp">

                <!-- Top Bar with Location and Track Prayers -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <!-- Location Button -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/button_location"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:layout_marginEnd="12dp"
                        android:backgroundTint="#20FFFFFF"
                        android:text="Detecting..."
                        android:textColor="@android:color/white"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        app:cornerRadius="18dp"
                        app:icon="@drawable/ic_location"
                        app:iconGravity="textStart"
                        app:iconSize="16dp"
                        app:iconTint="@android:color/white"
                        style="@style/Widget.Material3.Button.TextButton" />

                    <!-- Spacer -->
                    <View
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_weight="1" />

                    <!-- Track Prayers Button -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/button_track_prayers"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:backgroundTint="#20FFFFFF"
                        android:text="@string/track_prayers"
                        android:textColor="@android:color/white"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        app:cornerRadius="18dp"
                        app:icon="@drawable/ic_track"
                        app:iconGravity="textStart"
                        app:iconSize="16dp"
                        app:iconTint="@android:color/white"
                        style="@style/Widget.Material3.Button.TextButton" />

                </LinearLayout>

                <!-- Islamic Greeting -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/bismillah"
                    android:textColor="@android:color/white"
                    android:textSize="16sp"
                    android:textStyle="bold" />



                <!-- Current Prayer Display -->
                <TextView
                    android:id="@+id/text_current_prayer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:gravity="center"
                    android:textColor="@android:color/white"
                    android:textSize="32sp"
                    android:textStyle="bold"
                    tools:text="Asr" />

                <TextView
                    android:id="@+id/text_current_prayer_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:gravity="center"
                    android:textColor="@android:color/white"
                    android:textSize="48sp"
                    android:textStyle="bold"
                    tools:text="4:06" />

                <TextView
                    android:id="@+id/text_current_prayer_period"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textColor="@android:color/white"
                    android:textSize="24sp"
                    tools:text="PM" />



            </LinearLayout>

            <!-- Notification Settings Button -->
            <Button
                android:id="@+id/button_notification_settings"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="16dp"
                android:layout_marginHorizontal="16dp"
                android:background="@drawable/button_rounded"
                android:text="🔔 Notification Settings"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- Date Navigation with Hijri Date -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginHorizontal="16dp"
                android:background="@drawable/prayer_item_background"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="12dp">

                <ImageButton
                    android:id="@+id/button_previous_day"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_arrow_left"
                    app:tint="@android:color/white" />

                <!-- Hijri Date Display -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/text_hijri_date"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:textColor="@android:color/white"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        tools:text="Dhul-Qidah 29, 1446 AH" />

                    <TextView
                        android:id="@+id/text_selected_date"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:gravity="center"
                        android:textColor="#B3FFFFFF"
                        android:textSize="12sp"
                        tools:text="Today" />

                </LinearLayout>

                <ImageButton
                    android:id="@+id/button_next_day"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_arrow_right"
                    app:tint="@android:color/white" />

            </LinearLayout>

            <!-- Prayer Times RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view_prayer_times"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:nestedScrollingEnabled="false"
                tools:itemCount="5"
                tools:listitem="@layout/item_prayer_time" />



            <!-- Error Message -->
            <TextView
                android:id="@+id/text_error"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/bg_error_message"
                android:gravity="center"
                android:padding="16dp"
                android:textColor="@color/error"
                android:textSize="14sp"
                android:visibility="gone"
                tools:text="Unable to load prayer times. Please check your internet connection."
                tools:visibility="visible" />

        </LinearLayout>

</androidx.core.widget.NestedScrollView>
