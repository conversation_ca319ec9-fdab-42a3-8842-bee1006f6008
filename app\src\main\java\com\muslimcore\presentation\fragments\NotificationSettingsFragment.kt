package com.muslimcore.presentation.fragments

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.muslimcore.databinding.FragmentNotificationSettingsBinding
import com.muslimcore.presentation.services.PrayerService
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class NotificationSettingsFragment : Fragment() {

    private var _binding: FragmentNotificationSettingsBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var notificationPrefs: SharedPreferences
    
    companion object {
        private const val PREF_NAME = "notification_settings"
        private const val PREF_PRAYER_ALERTS_ENABLED = "prayer_alerts_enabled"
        private const val PREF_FULLSCREEN_ALERT_ENABLED = "fullscreen_alert_enabled"
        private const val PREF_ALERT_TYPE = "alert_type"
        
        // Alert types
        const val ALERT_TYPE_VIBRATION = "vibration"
        const val ALERT_TYPE_ATHAN = "athan"
        const val ALERT_TYPE_PHONE_SOUND = "phone_sound"
        const val ALERT_TYPE_VIBRATION_AND_SOUND = "vibration_and_sound"
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentNotificationSettingsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        notificationPrefs = requireContext().getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
        
        setupClickListeners()
        loadSettings()
        setupSwitchListeners()
    }

    private fun setupClickListeners() {
        binding.buttonBack.setOnClickListener {
            requireActivity().onBackPressed()
        }

        binding.buttonSaveSettings.setOnClickListener {
            saveSettings()
            requireActivity().onBackPressed()
        }
    }

    private fun setupSwitchListeners() {
        // Show/hide alert options based on prayer alerts switch
        binding.switchPrayerAlerts.setOnCheckedChangeListener { _, isChecked ->
            binding.layoutAlertOptions.visibility = if (isChecked) View.VISIBLE else View.GONE
        }
    }

    private fun loadSettings() {
        // Load prayer alerts setting
        val prayerAlertsEnabled = notificationPrefs.getBoolean(PREF_PRAYER_ALERTS_ENABLED, true)
        binding.switchPrayerAlerts.isChecked = prayerAlertsEnabled
        binding.layoutAlertOptions.visibility = if (prayerAlertsEnabled) View.VISIBLE else View.GONE

        // Load fullscreen alert setting
        binding.switchFullscreenAlert.isChecked =
            notificationPrefs.getBoolean(PREF_FULLSCREEN_ALERT_ENABLED, true)

        // Load alert type
        val alertType = notificationPrefs.getString(PREF_ALERT_TYPE, ALERT_TYPE_VIBRATION_AND_SOUND)
        when (alertType) {
            ALERT_TYPE_VIBRATION -> binding.radioVibrationOnly.isChecked = true
            ALERT_TYPE_ATHAN -> binding.radioAthanSound.isChecked = true
            ALERT_TYPE_PHONE_SOUND -> binding.radioPhoneSound.isChecked = true
            ALERT_TYPE_VIBRATION_AND_SOUND -> binding.radioVibrationAndSound.isChecked = true
        }
    }

    private fun saveSettings() {
        val editor = notificationPrefs.edit()

        // Save prayer alerts setting
        editor.putBoolean(PREF_PRAYER_ALERTS_ENABLED, binding.switchPrayerAlerts.isChecked)

        // Save fullscreen alert setting
        editor.putBoolean(PREF_FULLSCREEN_ALERT_ENABLED, binding.switchFullscreenAlert.isChecked)

        // Save alert type
        val alertType = when (binding.radioGroupAlertType.checkedRadioButtonId) {
            binding.radioVibrationOnly.id -> ALERT_TYPE_VIBRATION
            binding.radioAthanSound.id -> ALERT_TYPE_ATHAN
            binding.radioPhoneSound.id -> ALERT_TYPE_PHONE_SOUND
            binding.radioVibrationAndSound.id -> ALERT_TYPE_VIBRATION_AND_SOUND
            else -> ALERT_TYPE_VIBRATION_AND_SOUND
        }
        editor.putString(PREF_ALERT_TYPE, alertType)

        editor.apply()

        // Update background service based on settings
        updateBackgroundService()
    }
    
    private fun updateBackgroundService() {
        val prayerAlertsEnabled = binding.switchPrayerAlerts.isChecked

        if (prayerAlertsEnabled) {
            // Start background service
            startPrayerService()
        } else {
            // Stop background service
            stopPrayerService()
        }
    }
    
    private fun startPrayerService() {
        PrayerService.startService(requireContext())
    }

    private fun stopPrayerService() {
        PrayerService.stopService(requireContext())
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
