package com.muslimcore.presentation.services

import android.app.*
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import androidx.lifecycle.lifecycleScope
import com.muslimcore.R
import com.muslimcore.data.local.managers.LocationManager
import com.muslimcore.data.local.managers.PrayerTimeManager
import com.muslimcore.presentation.activities.PrayerAlertActivity
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject

@AndroidEntryPoint
class PrayerService : Service() {

    @Inject
    lateinit var locationManager: LocationManager
    
    @Inject
    lateinit var prayerTimeManager: PrayerTimeManager
    
    private lateinit var notificationPrefs: SharedPreferences
    private var serviceJob: Job? = null
    
    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "prayer_service_channel"
        private const val PRAYER_ALERT_CHANNEL_ID = "prayer_alert_channel"
        private const val PERSISTENT_NOTIFICATION_ID = 1003
        private const val PERSISTENT_CHANNEL_ID = "persistent_prayer_channel"
        
        fun startService(context: Context) {
            val intent = Intent(context, PrayerService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
        
        fun stopService(context: Context) {
            val intent = Intent(context, PrayerService::class.java)
            context.stopService(intent)
        }
    }

    override fun onCreate() {
        super.onCreate()
        notificationPrefs = getSharedPreferences("notification_settings", Context.MODE_PRIVATE)
        createNotificationChannels()

        // CRITICAL: Start foreground immediately to prevent crashes
        startForegroundService()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // Service is already foreground from onCreate()
        // Start monitoring in background thread to prevent blocking
        startPrayerMonitoring()
        return START_STICKY // Restart if killed
    }

    override fun onBind(intent: Intent?): IBinder? = null

    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // Service channel
            val serviceChannel = NotificationChannel(
                CHANNEL_ID,
                "Prayer Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Background service for prayer notifications"
                setShowBadge(false)
            }

            // Prayer alert channel
            val prayerAlertChannel = NotificationChannel(
                PRAYER_ALERT_CHANNEL_ID,
                "Prayer Alerts",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Prayer time alert notifications"
                setShowBadge(true)
                enableVibration(true)
                enableLights(true)
            }

            // Persistent notification channel
            val persistentChannel = NotificationChannel(
                PERSISTENT_CHANNEL_ID,
                "Persistent Prayer Notification",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Shows next prayer in notification bar"
                setShowBadge(false)
                enableVibration(false)
                enableLights(false)
            }

            notificationManager.createNotificationChannel(serviceChannel)
            notificationManager.createNotificationChannel(prayerAlertChannel)
            notificationManager.createNotificationChannel(persistentChannel)
        }
    }

    private fun startForegroundService() {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Prayer Service")
            .setContentText("Monitoring prayer times")
            .setSmallIcon(R.drawable.prayer_icon)
            .setOngoing(true)
            .setShowWhen(false)
            .build()

        startForeground(NOTIFICATION_ID, notification)
    }

    private fun startPrayerMonitoring() {
        serviceJob = CoroutineScope(Dispatchers.Default).launch {
            // Initial delay to ensure service is fully started
            delay(2000)

            while (isActive) {
                try {
                    val location = locationManager.getCurrentLocation()
                    if (location != null) {
                        val prayerTimes = prayerTimeManager.calculatePrayerTimes(
                            location.latitude,
                            location.longitude
                        )

                        // Check for prayer time alerts
                        checkPrayerTimeAlerts(prayerTimes)

                        // Update persistent notification if enabled
                        updatePersistentNotification(prayerTimes)
                    } else {
                        // If no location, show default persistent notification
                        updatePersistentNotificationWithDefault()
                    }

                    // Wait 30 seconds before next check
                    delay(30000)

                } catch (e: Exception) {
                    // Handle errors gracefully - show default notification
                    updatePersistentNotificationWithDefault()
                    delay(60000) // Wait longer on error
                }
            }
        }
    }

    private fun checkPrayerTimeAlerts(prayerTimes: PrayerTimeManager.PrayerTimes) {
        val prayerAlertsEnabled = notificationPrefs.getBoolean("prayer_alerts_enabled", true)
        if (!prayerAlertsEnabled) return
        
        val currentPrayer = prayerTimeManager.isPrayerTime(prayerTimes)
        if (currentPrayer != null) {
            // Show prayer alert
            showPrayerAlert(currentPrayer)
        }
    }

    private fun updatePersistentNotification(prayerTimes: PrayerTimeManager.PrayerTimes) {
        val persistentEnabled = notificationPrefs.getBoolean("persistent_notification_enabled", false)
        if (!persistentEnabled) {
            // Remove persistent notification if disabled
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.cancel(PERSISTENT_NOTIFICATION_ID)
            return
        }

        try {
            // Simple, bulletproof logic - no complex calculations
            val now = Calendar.getInstance()
            val timeFormat = SimpleDateFormat("h:mm a", Locale.getDefault())

            val prayers = listOf(
                "Fajr" to prayerTimes.fajr,
                "Dhuhr" to prayerTimes.dhuhr,
                "Asr" to prayerTimes.asr,
                "Maghrib" to prayerTimes.maghrib,
                "Isha" to prayerTimes.isha
            )

            // Find next prayer - simple approach
            var nextPrayerName = "Fajr"
            var nextPrayerTime = prayerTimes.fajr

            for ((name, time) in prayers) {
                if (now.timeInMillis < time.timeInMillis) {
                    nextPrayerName = name
                    nextPrayerTime = time
                    break
                }
            }

            val prayerTimeText = timeFormat.format(nextPrayerTime.time)
            showPersistentNotification(nextPrayerName, prayerTimeText)
        } catch (e: Exception) {
            // Fallback to default
            showPersistentNotification("Prayer", "Loading...")
        }
    }

    private fun updatePersistentNotificationWithDefault() {
        val persistentEnabled = notificationPrefs.getBoolean("persistent_notification_enabled", false)
        if (persistentEnabled) {
            showPersistentNotification("Prayer", "Loading...")
        }
    }

    private fun showPersistentNotification(prayerName: String, prayerTime: String) {
        val notification = NotificationCompat.Builder(this, PERSISTENT_CHANNEL_ID)
            .setContentTitle("Next Prayer: $prayerName $prayerTime")
            .setContentText("Muslim Core")
            .setSmallIcon(R.drawable.prayer_icon)
            .setOngoing(true)
            .setShowWhen(false)
            .setOnlyAlertOnce(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(PERSISTENT_NOTIFICATION_ID, notification)
    }



    private fun showPrayerAlert(prayer: PrayerTimeManager.Prayer) {
        val fullscreenEnabled = notificationPrefs.getBoolean("fullscreen_alert_enabled", true)

        if (fullscreenEnabled) {
            // Show full-screen alert
            val prayerName = prayer.name
            val timeFormat = SimpleDateFormat("h:mm a", Locale.getDefault())
            val currentTime = timeFormat.format(Date())

            val intent = PrayerAlertActivity.createIntent(this, prayerName, currentTime)
            startActivity(intent)
        } else {
            // Show regular notification
            showPrayerNotification(prayer)
        }
    }

    private fun showPrayerNotification(prayer: PrayerTimeManager.Prayer) {
        val notification = NotificationCompat.Builder(this, PRAYER_ALERT_CHANNEL_ID)
            .setContentTitle("Prayer Time: ${prayer.name}")
            .setContentText("It's time for ${prayer.name} prayer")
            .setSmallIcon(R.drawable.prayer_icon)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .setDefaults(NotificationCompat.DEFAULT_ALL)
            .setVibrate(longArrayOf(0, 500, 250, 500))
            .build()

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(prayer.hashCode(), notification)
    }

    override fun onDestroy() {
        super.onDestroy()
        serviceJob?.cancel()
    }
}
